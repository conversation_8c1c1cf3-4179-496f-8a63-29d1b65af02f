# 依赖库安装说明:
# pip install pyodbc tqdm python-docx python-pptx openpyxl xlrd

import argparse
import json
import os
import sys
import uuid
import hashlib
import shutil
import pyodbc
import datetime
import time
import psutil
import threading
import queue
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
from contextlib import contextmanager

# 全局资源管理
class ResourceManager:
    def __init__(self, max_memory_percent=80, max_office_processes=2):
        self.max_memory_percent = max_memory_percent
        self.max_office_processes = max_office_processes
        self.office_semaphore = threading.Semaphore(max_office_processes)
        self.db_connections = queue.Queue()
        self.process_monitor = None

    def init_db_pool(self, conn_params, pool_size=10):
        """初始化数据库连接池"""
        for _ in range(pool_size):
            try:
                conn = pyodbc.connect(conn_params, timeout=10)
                self.db_connections.put(conn)
            except Exception as e:
                print(f"创建数据库连接失败: {e}")

    def get_db_connection(self):
        """获取数据库连接"""
        try:
            return self.db_connections.get(timeout=30)
        except queue.Empty:
            raise Exception("无法获取数据库连接")

    def return_db_connection(self, conn):
        """归还数据库连接"""
        self.db_connections.put(conn)

    @contextmanager
    def office_process_limit(self):
        """限制libreoffice进程数"""
        acquired = self.office_semaphore.acquire(timeout=300)
        if not acquired:
            raise Exception("等待libreoffice进程超时")
        try:
            yield
        finally:
            self.office_semaphore.release()

    def check_memory(self):
        """检查内存使用情况"""
        memory = psutil.virtual_memory()
        return memory.percent < self.max_memory_percent

    def cleanup_zombie_processes(self):
        """清理僵尸进程"""
        for proc in psutil.process_iter(['name', 'status']):
            try:
                if 'soffice' in proc.info['name'] and proc.info['status'] == 'zombie':
                    proc.kill()
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

    def start_monitoring(self):
        """启动资源监控"""
        def monitor():
            while True:
                self.cleanup_zombie_processes()
                if not self.check_memory():
                    print("警告：内存使用率过高")
                time.sleep(60)
        
        self.process_monitor = threading.Thread(target=monitor, daemon=True)
        self.process_monitor.start()

# 全局资源管理器
resource_mgr = ResourceManager()

# 读取配置
def load_config(config_path):
    with open(config_path, 'r', encoding='utf-8') as f:
        return json.load(f)

# 数据库连接
def get_db_conn(cfg):
    conn_str = (
        f"DRIVER={cfg['driver']};"
        f"SERVER={cfg['server']};"
        f"DATABASE={cfg['database']};"
        f"UID={cfg['uid']};"
        f"PWD={cfg['pwd']};"
        f"Encrypt={cfg.get('encrypt', 'yes')};"
        f"TrustServerCertificate={cfg.get('trustServerCertificate', 'yes')};"
        "CHARSET=UTF-16"
    )
    try:
        conn = pyodbc.connect(conn_str, timeout=10)
        print("数据库连接成功。")
        return conn
    except Exception as e:
        print(f"数据库连接失败: {e}")
        sys.exit(1)

# 检查/创建表
def ensure_table(conn):
    cursor = conn.cursor()
    # 检查表是否存在
    check_sql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Documents'"
    cursor.execute(check_sql)
    exists = cursor.fetchone()[0]
    if not exists:
        create_sql = '''
        CREATE TABLE Documents (
            id INT IDENTITY(1,1) PRIMARY KEY,
            doc_create_time DATETIME,
            file_name NVARCHAR(255),
            file_size BIGINT,
            doc_hash NVARCHAR(64),
            doc_content NVARCHAR(MAX),
            doc_creator NVARCHAR(255),
            upload_time DATETIME,
            uploader NVARCHAR(255),
            doc_keywords NVARCHAR(255),
            doc_title NVARCHAR(255),
            doc_abstract NVARCHAR(1024),
            doc_source NVARCHAR(255),
            doc_type NVARCHAR(64),
            doc_department NVARCHAR(255),
            doc_profession NVARCHAR(255),
            doc_analysis_time DATETIME DEFAULT '1999-01-01 00:00'
        )
        '''
        try:
            cursor.execute(create_sql)
            conn.commit()
            print("表单已创建。")
        except Exception as e:
            print(f"表单创建失败: {e}")
            sys.exit(1)
    else:
        # 检查file_size字段是否存在，不存在则添加
        try:
            cursor.execute("SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME='Documents' AND COLUMN_NAME='file_size'")
            col_exists = cursor.fetchone()[0]
            if not col_exists:
                cursor.execute("ALTER TABLE Documents ADD file_size BIGINT")
                conn.commit()
                print("已为Documents表添加file_size字段。")
        except Exception as e:
            print(f"file_size字段检查/添加失败: {e}")
        print("表单已存在，无需创建。")

# 获取所有目标文件
def get_all_files(input_dir):
    exts = ('.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.wps', '.et')
    files = []
    for root, _, filenames in os.walk(input_dir):
        for fn in filenames:
            if fn.lower().endswith(exts):
                files.append(os.path.join(root, fn))
    return files

# 生成20位uuid
def uuid20():
    return uuid.uuid4().hex[:20]

# 计算文件hash
def file_hash(path):
    h = hashlib.sha256()
    with open(path, 'rb') as f:
        while True:
            chunk = f.read(8192)
            if not chunk:
                break
            h.update(chunk)
    return h.hexdigest()

# 数据库插入重试机制
def db_insert_with_retry(cursor, sql, params, max_retries=5, delay=2):
    for attempt in range(max_retries):
        try:
            cursor.execute(sql, params)
            return True
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"数据库插入失败，重试中({attempt+1}/{max_retries})... 错误: {e}")
                time.sleep(delay)
            else:
                print(f"数据库插入最终失败: {e}")
                return False

# 多线程处理单个文件
def process_file(f, output_dir):
    try:
        if not resource_mgr.check_memory():
            return {"file": f, "status": "error", "msg": "系统内存不足，跳过处理"}

        ext = os.path.splitext(f)[1]
        doc_hash = file_hash(f)

        # 从连接池获取连接
        conn = resource_mgr.get_db_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM Documents WHERE doc_hash = ?", doc_hash)
            exists = cursor.fetchone()[0]
            if exists:
                return {"file": f, "status": "skip_hash", "msg": "hash已存在"}

            # 限制libreoffice进程数
            with resource_mgr.office_process_limit():
                from extract_utils import extract_file_content_and_meta, get_file_create_time, extract_doc_content_libreoffice
                doc_content, doc_create_time, doc_creator = extract_file_content_and_meta(f)
                if not doc_content:
                    doc_content = extract_doc_content_libreoffice(f)
                    if not doc_content:
                        return {"file": f, "status": "skip_content", "msg": "无法提取正文"}

            if not doc_create_time:
                doc_create_time = get_file_create_time(f)
            if not doc_creator:
                doc_creator = ""

            new_name = f"{uuid20()}{ext}"
            out_path = os.path.join(output_dir, new_name)
            shutil.copy2(f, out_path)
            file_size = os.path.getsize(out_path)
            file_name = new_name
            upload_time = datetime.datetime.now()
            uploader = "Zhang Pengfei"

            sql = (
                "INSERT INTO Documents (doc_create_time, file_name, file_size, doc_hash, doc_content, doc_creator, upload_time, uploader, doc_keywords, doc_title, doc_abstract, doc_source, doc_type, doc_department, doc_profession, doc_analysis_time) "
                "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            )
            params = (
                doc_create_time, file_name, file_size, doc_hash, doc_content, doc_creator, upload_time, uploader,
                "", "", "", "", "", "", "", datetime.datetime(1999, 1, 1, 0, 0)
            )
            ok = db_insert_with_retry(cursor, sql, params)
            if ok:
                conn.commit()
                return {"file": f, "status": "success", "msg": f"已入库:{file_name}"}
            else:
                return {"file": f, "status": "fail", "msg": "数据库插入失败"}
        finally:
            resource_mgr.return_db_connection(conn)
    except Exception as e:
        return {"file": f, "status": "error", "msg": str(e)}

# 主流程
def save_progress(progress_file, processed_files):
    """保存处理进度到JSON文件"""
    try:
        with open(progress_file, 'w', encoding='utf-8') as f:
            json.dump({
                'processed_files': list(processed_files),
                'timestamp': datetime.datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"保存进度失败: {e}")

def load_progress(progress_file):
    """从JSON文件加载处理进度"""
    try:
        if os.path.exists(progress_file):
            with open(progress_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return set(data['processed_files'])
        return set()
    except Exception as e:
        print(f"加载进度失败: {e}")
        return set()

def main():
    parser = argparse.ArgumentParser(description="文档保存系统")
    parser.add_argument('--config', default='nbfwq.json', help='数据库配置文件路径')
    parser.add_argument('--input_dir', required=True, help='输入目录')
    parser.add_argument('--output_dir', required=True, help='输出目录')
    parser.add_argument('--threads', type=int, default=4, help='并发线程数，默认4')
    parser.add_argument('--progress_file', default='process_progress.json', help='进度保存文件路径')
    parser.add_argument('--resume', action='store_true', help='是否从上次进度继续处理')
    args = parser.parse_args()

    cfg = load_config(args.config)
    conn = get_db_conn(cfg)
    ensure_table(conn)

    os.makedirs(args.output_dir, exist_ok=True)
    files = get_all_files(args.input_dir)
    print(f"共发现{len(files)}个待处理文档。")

    # 加载上次进度
    processed_files = set()
    if args.resume:
        processed_files = load_progress(args.progress_file)
        print(f"从进度文件加载已处理文件数: {len(processed_files)}")

    # 过滤出未处理的文件
    files_to_process = [f for f in files if f not in processed_files]
    print(f"待处理文件数: {len(files_to_process)}")

    # 初始化资源管理器
    resource_mgr.start_monitoring()

    # 构造连接串
    conn_params = (
        f"DRIVER={cfg['driver']};"
        f"SERVER={cfg['server']};"
        f"DATABASE={cfg['database']};"
        f"UID={cfg['uid']};"
        f"PWD={cfg['pwd']};"
        f"Encrypt={cfg.get('encrypt', 'yes')};"
        f"TrustServerCertificate={cfg.get('trustServerCertificate', 'yes')};"
        "CHARSET=UTF-16"
    )
    
    # 初始化数据库连接池
    resource_mgr.init_db_pool(conn_params, pool_size=min(args.threads * 2, 10))

    # 分批处理文件，减少内存压力
    batch_size = 1000
    results = []
    try:
        for i in range(0, len(files_to_process), batch_size):
            batch_files = files_to_process[i:i+batch_size]
            print(f"\n处理第{i//batch_size + 1}批文件（{len(batch_files)}个）...")
            
            with ThreadPoolExecutor(max_workers=args.threads) as executor:
                future_to_file = {executor.submit(process_file, f, args.output_dir): f for f in batch_files}
                for future in tqdm(as_completed(future_to_file), total=len(batch_files), desc="多线程处理", ncols=100):
                    res = future.result()
                    results.append(res)
                    tqdm.write(f"{os.path.basename(res['file'])}: {res['status']} - {res['msg']}")
                    # 更新并保存进度
                    if res['status'] not in ['error', 'fail']:
                        processed_files.add(res['file'])
                        save_progress(args.progress_file, processed_files)

    except KeyboardInterrupt:
        print("\n检测到中断，保存当前进度...")
        save_progress(args.progress_file, processed_files)
        sys.exit(1)

    print("处理完成。")

    # 清理/data/save_doc/目录下未入库的文件
    print("开始清理/data/save_doc/目录下未入库的文件...")
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT file_name FROM Documents")
        db_files = set(row[0] for row in cursor.fetchall())
        save_dir = args.output_dir
        for fn in os.listdir(save_dir):
            fpath = os.path.join(save_dir, fn)
            if os.path.isfile(fpath) and fn not in db_files:
                try:
                    os.remove(fpath)
                    print(f"已清理未入库文件: {fn}")
                except Exception as e:
                    print(f"删除文件{fn}失败: {e}")
        print("清理完成。")
    except Exception as e:
        print(f"清理未入库文件时出错: {e}")

    # 处理完成后删除进度文件
    try:
        if os.path.exists(args.progress_file):
            os.remove(args.progress_file)
    except Exception as e:
        print(f"删除进度文件失败: {e}")

if __name__ == '__main__':
    main()
