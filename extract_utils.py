import os
import subprocess
import datetime

from docx import Document as DocxDocument
from pptx import Presentation as PptxPresentation
import xlrd
import openpyxl

def get_file_size_mb(filepath):
    """获取文件大小（MB）"""
    return os.path.getsize(filepath) / (1024 * 1024)

def get_convert_timeout(filepath, default_timeout=120):
    """根据文件大小确定转换超时时间"""
    size_mb = get_file_size_mb(filepath)
    if size_mb > 1:  # 大于1MB的文件
        # 每MB增加30秒，最少120秒，最多600秒
        timeout = min(max(default_timeout, int(size_mb * 30)), 600)
        return timeout
    return default_timeout

def extract_docx_content_and_meta(filepath):
    try:
        doc = DocxDocument(filepath)
        text = "\n".join([p.text for p in doc.paragraphs])
        props = doc.core_properties
        create_time = props.created if props.created else None
        creator = props.author if props.author else ""
        return text, create_time, creator
    except Exception:
        return "", None, ""

def extract_pptx_content_and_meta(filepath):
    try:
        prs = PptxPresentation(filepath)
        text = []
        for slide in prs.slides:
            for shape in slide.shapes:
                if hasattr(shape, "text"):
                    text.append(shape.text)
        # pptx元数据
        props = prs.core_properties
        create_time = props.created if hasattr(props, "created") else None
        creator = props.author if hasattr(props, "author") else ""
        return "\n".join(text), create_time, creator
    except Exception:
        return "", None, ""

def extract_xlsx_content_and_meta(filepath):
    try:
        # 检查文件大小并调整超时时间
        timeout = get_convert_timeout(filepath)
        print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")
        
        wb = openpyxl.load_workbook(filepath, read_only=True)
        text = []
        for ws in wb.worksheets:
            for row in ws.iter_rows(values_only=True):
                text.append("\t".join([str(cell) if cell is not None else "" for cell in row]))
        props = wb.properties
        create_time = props.created if hasattr(props, "created") else None
        creator = props.creator if hasattr(props, "creator") else ""
        return "\n".join(text), create_time, creator
    except Exception as e:
        print(f"处理Excel文件失败: {e}")
        return "", None, ""

from save_documents import resource_mgr

def extract_doc_content_libreoffice(filepath):
    # 用libreoffice转txt
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")
            
            out_txt = filepath + ".txt"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "txt:Text",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            try:
                with open(out_txt, "r", encoding="utf-8", errors="ignore") as f:
                    content = f.read()
                return content
            finally:
                try:
                    if os.path.exists(out_txt):
                        os.remove(out_txt)
                except:
                    pass
    except subprocess.TimeoutExpired:
        print(f"文档转换超时: {filepath}")
        return ""
    except Exception as e:
        print(f"文档转换失败: {e}")
        return ""

def extract_doc_content_antiword(filepath):
    # 用antiword命令行工具提取doc文本
    try:
        result = subprocess.run(['antiword', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_xls_content_xls2csv(filepath):
    # 用xls2csv命令行工具提取xls文本
    try:
        result = subprocess.run(['xls2csv', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_ppt_content_catppt(filepath):
    # 用catppt命令行工具提取ppt文本
    try:
        result = subprocess.run(['catppt', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_xls_content_pyexcel(filepath):
    try:
        import pyexcel
        records = pyexcel.iget_records(file_name=filepath)
        text_rows = []
        for record in records:
            text_rows.append("\t".join([str(v) for v in record.values()]))
        return "\n".join(text_rows)
    except Exception:
        return ""

def extract_xls_content_libreoffice_xlsx(filepath):
    # 用libreoffice转xlsx再用openpyxl读取
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")
            
            out_xlsx = filepath + ".xlsx"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "xlsx",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            if os.path.exists(out_xlsx):
                try:
                    text = extract_xlsx_content_and_meta(out_xlsx)[0]
                    return text
                finally:
                    try:
                        os.remove(out_xlsx)
                    except:
                        pass
        return ""
    except subprocess.TimeoutExpired:
        print(f"文档转换超时: {filepath}")
        return ""
    except Exception as e:
        print(f"文档转换失败: {e}")
        return ""

def extract_xls_content_multimethod(filepath):
    # 1. xlrd
    try:
        wb = xlrd.open_workbook(filepath)
        text_rows = []
        for sheet in wb.sheets():
            for row_idx in range(sheet.nrows):
                row = sheet.row_values(row_idx)
                text_rows.append("\t".join([str(cell) for cell in row]))
        text = "\n".join(text_rows)
        if text.strip():
            return text
    except Exception:
        pass
    # 2. pyexcel
    text = extract_xls_content_pyexcel(filepath)
    if text.strip():
        return text
    # 3. xls2csv
    text = extract_xls_content_xls2csv(filepath)
    if text.strip():
        return text
    # 4. libreoffice转xlsx再openpyxl
    text = extract_xls_content_libreoffice_xlsx(filepath)
    return text

def extract_wps_content_antiword(filepath):
    # 尝试用antiword直接读取wps文件
    try:
        result = subprocess.run(['antiword', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_wps_content_txt(filepath):
    # 尝试直接转换为txt
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")
            
            out_txt = filepath + ".txt"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "txt:Text",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            if os.path.exists(out_txt):
                try:
                    with open(out_txt, "r", encoding="utf-8", errors="ignore") as f:
                        return f.read()
                finally:
                    try:
                        os.remove(out_txt)
                    except:
                        pass
        return ""
    except subprocess.TimeoutExpired:
        print(f"WPS转TXT超时: {filepath}")
        return ""
    except Exception as e:
        print(f"WPS转TXT失败: {e}")
        return ""

def extract_wps_content_libreoffice(filepath):
    # 用libreoffice将wps转为docx再读取
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")
            
            out_docx = filepath + ".docx"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "docx",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            if os.path.exists(out_docx):
                try:
                    text = extract_docx_content_and_meta(out_docx)[0]
                    return text
                finally:
                    try:
                        os.remove(out_docx)
                    except:
                        pass
        return ""
    except subprocess.TimeoutExpired:
        print(f"WPS转DOCX超时: {filepath}")
        return ""
    except Exception as e:
        print(f"WPS转DOCX失败: {e}")
        return ""

def extract_wps_content_multimethod(filepath):
    # 1. antiword
    text = extract_wps_content_antiword(filepath)
    if text.strip():
        return text
    # 2. 转txt
    text = extract_wps_content_txt(filepath)
    if text.strip():
        return text
    # 3. libreoffice转docx
    text = extract_wps_content_libreoffice(filepath)
    return text

def extract_et_content_xls2csv(filepath):
    # 尝试用xls2csv直接读取et文件
    try:
        result = subprocess.run(['xls2csv', filepath], stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=30)
        if result.returncode == 0:
            return result.stdout.decode('utf-8', errors='ignore')
        return ""
    except Exception:
        return ""

def extract_et_content_pyexcel(filepath):
    # 尝试用pyexcel读取et文件
    try:
        import pyexcel
        records = pyexcel.iget_records(file_name=filepath)
        text_rows = []
        for record in records:
            text_rows.append("\t".join([str(v) for v in record.values()]))
        return "\n".join(text_rows)
    except Exception:
        return ""

def extract_et_content_libreoffice(filepath):
    # 用libreoffice将et转为xlsx再读取
    try:
        with resource_mgr.office_process_limit():
            timeout = get_convert_timeout(filepath)
            print(f"文件大小: {get_file_size_mb(filepath):.2f}MB, 设置超时: {timeout}秒")
            
            out_xlsx = filepath + ".xlsx"
            cmd = [
                "libreoffice",
                "--headless",
                "--convert-to",
                "xlsx",
                "--outdir",
                os.path.dirname(filepath),
                filepath
            ]
            subprocess.run(cmd, check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, timeout=timeout)
            if os.path.exists(out_xlsx):
                try:
                    text = extract_xlsx_content_and_meta(out_xlsx)[0]
                    return text
                finally:
                    try:
                        os.remove(out_xlsx)
                    except:
                        pass
        return ""
    except subprocess.TimeoutExpired:
        print(f"ET转换超时: {filepath}")
        return ""
    except Exception as e:
        print(f"ET转换失败: {e}")
        return ""

def extract_et_content_multimethod(filepath):
    # 1. pyexcel
    text = extract_et_content_pyexcel(filepath)
    if text.strip():
        return text
    # 2. xls2csv
    text = extract_et_content_xls2csv(filepath)
    if text.strip():
        return text
    # 3. libreoffice转xlsx
    text = extract_et_content_libreoffice(filepath)
    return text

def truncate_text(text, max_length=10000):
    """截取文本，保留指定长度"""
    if not text:
        return text
    
    if len(text) <= max_length:
        return text

    # 尝试在句子或段落边界截断
    for pos in range(max_length - 1, max_length - 100, -1):
        if pos >= len(text):
            continue
        # 优先在段落处截断
        if text[pos] == '\n':
            return text[:pos].strip()
        # 其次在句号处截断
        if text[pos] in ['。', '.', '!', '！', '?', '？']:
            return text[:pos+1].strip()
    
    # 如果找不到合适的断点，直接截取
    return text[:max_length].strip()

def extract_file_content_and_meta(filepath):
    ext = os.path.splitext(filepath)[1].lower()
    text, create_time, creator = "", None, ""
    # docx
    if ext == ".docx":
        text, create_time, creator = extract_docx_content_and_meta(filepath)
    # pptx
    elif ext == ".pptx":
        text, create_time, creator = extract_pptx_content_and_meta(filepath)
    # xlsx
    elif ext == ".xlsx":
        text, create_time, creator = extract_xlsx_content_and_meta(filepath)
    # doc
    elif ext == ".doc":
        # 1. antiword
        text = extract_doc_content_antiword(filepath)
        if not text:
            # 2. libreoffice
            text = extract_doc_content_libreoffice(filepath)
        create_time, creator = None, ""
    # xls
    elif ext == ".xls":
        text = extract_xls_content_multimethod(filepath)
        create_time, creator = None, ""
    # ppt
    elif ext == ".ppt":
        # 1. catppt
        text = extract_ppt_content_catppt(filepath)
        # 2. libreoffice
        if not text:
            text = extract_doc_content_libreoffice(filepath)
        create_time, creator = None, ""
    # wps
    elif ext == ".wps":
        text = extract_wps_content_multimethod(filepath)
        create_time, creator = None, ""
    # et
    elif ext == ".et":
        text = extract_et_content_multimethod(filepath)
        create_time, creator = None, ""
    else:
        text, create_time, creator = "", None, ""

    # 截取文本
    if text:
        text = truncate_text(text)
        
    return text, create_time, creator

def get_file_create_time(filepath):
    # 文件系统时间
    stat = os.stat(filepath)
    return datetime.datetime.fromtimestamp(stat.st_mtime)
